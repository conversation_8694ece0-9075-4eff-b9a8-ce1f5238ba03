/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import type { TextBuffer } from './shared/text-buffer.js';
import type { Configuration } from '@inkbytefo/s647-shared';

/**
 * Input prompt props
 */
export interface InputPromptProps {
  buffer: TextBuffer;
  onSubmit: (value: string) => void;
  userMessages: readonly string[];
  onClearScreen: () => void;
  config: Configuration;
  placeholder?: string;
  focus?: boolean;
  inputWidth: number;
  suggestionsWidth: number;
  shellModeActive: boolean;
  setShellModeActive: (value: boolean) => void;
  getCommandSuggestions?: (partial: string) => string[];
}

/**
 * Input prompt component with rich text editing
 */
export const InputPrompt: React.FC<InputPromptProps> = ({
  buffer,
  onSubmit,
  userMessages,
  onClearScreen,
  config,
  placeholder = '  Type your message or @path/to/file',
  focus = true,
  inputWidth,
  suggestionsWidth,
  shellModeActive,
  setShellModeActive,
  getCommandSuggestions,
}) => {
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [justNavigatedHistory, setJustNavigatedHistory] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Handle input events
  useInput((input, key) => {
    if (!focus) return;

    // Handle special key combinations first
    if (key.ctrl) {
      switch (input) {
        case 'c':
          // Clear current input
          buffer.setText('');
          setHistoryIndex(-1);
          setShowSuggestions(false);
          return;
        case 'l':
          // Clear screen
          onClearScreen();
          return;
        case 'd':
          // Exit if buffer is empty
          if (buffer.text.trim() === '') {
            process.exit(0);
          }
          return;
        case 'r':
          // Reverse search (simplified)
          setShowSuggestions(true);
          return;
        case 'g':
          // Cancel current operation
          setShowSuggestions(false);
          setHistoryIndex(-1);
          return;
      }
    }

    // Handle Enter key
    if (key.return) {
      const trimmedText = buffer.text.trim();
      if (trimmedText) {
        onSubmit(trimmedText);
        buffer.setText('');
        setHistoryIndex(-1);
        setJustNavigatedHistory(false);
      }
      return;
    }

    // Handle history navigation
    if (key.upArrow && !key.shift) {
      if (userMessages.length > 0) {
        const newIndex = Math.min(historyIndex + 1, userMessages.length - 1);
        if (newIndex !== historyIndex) {
          setHistoryIndex(newIndex);
          const historyMessage = userMessages[userMessages.length - 1 - newIndex];
          if (historyMessage) {
            buffer.setText(historyMessage);
            setJustNavigatedHistory(true);
          }
        }
      }
      return;
    }

    if (key.downArrow && !key.shift) {
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        const historyMessage = userMessages[userMessages.length - 1 - newIndex];
        if (historyMessage) {
          buffer.setText(historyMessage);
          setJustNavigatedHistory(true);
        }
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        buffer.setText('');
        setJustNavigatedHistory(true);
      }
      return;
    }

    // Handle Tab for suggestions
    if (key.tab) {
      if (showSuggestions && suggestions.length > 0) {
        // Apply first suggestion
        const firstSuggestion = suggestions[0];
        if (firstSuggestion) {
          buffer.setText(firstSuggestion);
          setShowSuggestions(false);
        }
      } else {
        // Show suggestions
        updateSuggestions(buffer.text);
      }
      return;
    }

    // Handle Escape
    if (key.escape) {
      setShowSuggestions(false);
      return;
    }

    // Reset history navigation flag on any other input
    if (justNavigatedHistory) {
      setJustNavigatedHistory(false);
    }

    // Pass input to text buffer for processing
    buffer.handleInput(input, key);

    // Update suggestions after text changes (debounced)
    if (!key.ctrl && !key.meta) {
      setTimeout(() => {
        if (buffer.text && !justNavigatedHistory) {
          updateSuggestions(buffer.text);
        }
      }, 50); // Small delay to avoid excessive updates
    }
  }, { isActive: focus });

  // Update suggestions based on current text
  const updateSuggestions = useCallback((text: string) => {
    const newSuggestions: string[] = [];

    // Check for command suggestions
    if (text.startsWith('/') && getCommandSuggestions) {
      const commandSuggestions = getCommandSuggestions(text);
      newSuggestions.push(...commandSuggestions);
    }
    // Check for @file syntax
    else if (text.includes('@')) {
      const atIndex = text.lastIndexOf('@');
      const pathPart = text.slice(atIndex + 1);

      if (pathPart.length > 0) {
        // Simple file suggestions (would be enhanced with actual file system access)
        const commonFiles = [
          'package.json',
          'README.md',
          'src/',
          'dist/',
          '.env',
          'tsconfig.json',
        ];

        newSuggestions.push(
          ...commonFiles
            .filter(file => file.toLowerCase().includes(pathPart.toLowerCase()))
            .map(file => text.slice(0, atIndex + 1) + file)
        );
      }
    }

    setSuggestions(newSuggestions.slice(0, 5)); // Limit to 5 suggestions
    setShowSuggestions(newSuggestions.length > 0);
  }, [getCommandSuggestions]);

  // Update suggestions when text changes
  useEffect(() => {
    if (buffer.text && !justNavigatedHistory) {
      updateSuggestions(buffer.text);
    } else {
      setShowSuggestions(false);
    }
  }, [buffer.text, justNavigatedHistory, updateSuggestions]);

  // Render input lines
  const renderInputLines = () => {
    const lines = buffer.viewportVisualLines;
    const [cursorRow, cursorCol] = buffer.visualCursor;
    const scrollRow = buffer.visualScrollRow;

    return lines.map((line, index) => {
      const actualRow = scrollRow + index;
      const isCurrentLine = actualRow === cursorRow;
      
      if (isCurrentLine) {
        // Enhanced cursor visualization
        const beforeCursor = line.slice(0, cursorCol);
        const atCursor = line[cursorCol] || ' ';
        const afterCursor = line.slice(cursorCol + 1);

        return (
          <Box key={index}>
            <Text>{beforeCursor}</Text>
            <Text
              backgroundColor={focus ? "white" : "gray"}
              color={focus ? "black" : "white"}
              bold={focus}
            >
              {atCursor}
            </Text>
            <Text>{afterCursor}</Text>
          </Box>
        );
      }
      
      return (
        <Box key={index}>
          <Text>{line || ' '}</Text>
        </Box>
      );
    });
  };

  return (
    <Box flexDirection="column">
      {/* Input area */}
      <Box
        borderStyle="single"
        borderColor="blue"
        padding={1}
        width={inputWidth}
        flexDirection="column"
      >
        {/* Placeholder or input content */}
        {buffer.text === '' ? (
          <Text color="gray" dimColor>
            {placeholder}
          </Text>
        ) : (
          <Box flexDirection="column">
            {renderInputLines()}
          </Box>
        )}
        
        {/* Status line */}
        <Box marginTop={1}>
          <Text color="cyan" dimColor>
            {shellModeActive ? '🐚 Shell Mode' : '💬 Chat Mode'} |
            Lines: {buffer.lines.length} |
            Chars: {buffer.text.length} |
            Cursor: {buffer.visualCursor[0] + 1}:{buffer.visualCursor[1] + 1}
            {buffer.text.includes('@') && ' | 📁 File reference detected'}
          </Text>
        </Box>
      </Box>

      {/* Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <Box
          marginTop={1}
          borderStyle="single"
          borderColor="yellow"
          padding={1}
          width={Math.min(suggestionsWidth, inputWidth)}
        >
          <Box flexDirection="column">
            <Text color="yellow" bold>
              💡 Suggestions (Tab to apply):
            </Text>
            {suggestions.map((suggestion, index) => (
              <Box key={index} marginLeft={2}>
                <Text color={index === 0 ? 'green' : 'gray'}>
                  {index === 0 ? '→ ' : '  '}
                  {suggestion}
                </Text>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* Help text */}
      <Box marginTop={1}>
        <Text color="gray" dimColor>
          ↑↓ History | Tab Suggestions | Ctrl+C Clear | Ctrl+L Clear Screen | 
          Ctrl+D Exit | @ File Reference | / Commands
        </Text>
      </Box>
    </Box>
  );
};
