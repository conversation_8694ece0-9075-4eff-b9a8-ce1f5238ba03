# S647 Refactored Environment Configuration
# Copy this file to .env and fill in your API keys

# ============================================================================
# AI PROVIDERS - Add your API keys here
# ============================================================================

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=16384
OPENAI_TEMPERATURE=0.7

# Anthropic Configuration
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=16384
ANTHROPIC_TEMPERATURE=0.7

# Google Gemini Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com
GOOGLE_MODEL=gemini-pro
GOOGLE_MAX_TOKENS=16384
GOOGLE_TEMPERATURE=0.7

# Mistral Configuration
# Get your API key from: https://console.mistral.ai/
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_BASE_URL=https://api.mistral.ai
MISTRAL_MODEL=mistral-large-latest
MISTRAL_MAX_TOKENS=16384
MISTRAL_TEMPERATURE=0.7

# OpenRouter Configuration
# Get your API key from: https://openrouter.ai/keys
# OpenRouter provides access to multiple AI models through a single API
# Popular models: openai/gpt-4, anthropic/claude-3-sonnet, google/gemini-pro,
#                 mistral/mistral-large, meta-llama/llama-3-70b-instruct
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=openai/gpt-3.5-turbo
OPENROUTER_SITE_NAME=S647
OPENROUTER_SITE_URL=https://s647.dev
OPENROUTER_MAX_TOKENS=16384
OPENROUTER_TEMPERATURE=0.7

# ============================================================================
# DEFAULT PROVIDER
# ============================================================================
# Available providers: openai, anthropic, google, mistral, openrouter
DEFAULT_PROVIDER=openai

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================

# Logging
LOG_LEVEL=info
LOG_FORMAT=text
DEBUG=false

# UI Settings
UI_THEME=dark
UI_ANIMATIONS=true
UI_SHOW_TIMESTAMPS=true
UI_SHOW_TOKEN_COUNT=true
UI_MAX_HISTORY_LINES=1000

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30000
RETRY_ATTEMPTS=3
RETRY_DELAY=1000

# Security
SANDBOX_ENABLED=false
ENCRYPTION_ENABLED=false
TELEMETRY_ENABLED=false

# ============================================================================
# MCP (Model Context Protocol) SETTINGS
# ============================================================================
MCP_ENABLED=true
MCP_SERVERS_CONFIG_PATH=./mcp-servers.json

# ============================================================================
# MEMORY MANAGEMENT
# ============================================================================
MEMORY_ENABLED=true
MEMORY_MAX_SIZE=100
MEMORY_TTL=3600

# ============================================================================
# FILE INTEGRATION
# ============================================================================
FILE_INTEGRATION_ENABLED=true
FILE_MAX_SIZE=10485760
FILE_ALLOWED_EXTENSIONS=.txt,.md,.js,.ts,.tsx,.jsx,.py,.java,.cpp,.c,.h,.css,.html,.json,.yaml,.yml,.xml,.sql,.sh,.bat,.ps1

# ============================================================================
# DEVELOPMENT SETTINGS
# ============================================================================
NODE_ENV=production
PORT=3000
